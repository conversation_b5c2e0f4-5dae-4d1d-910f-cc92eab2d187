# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller spec file for Voice Streaming Widget

This file configures how the voice widget should be packaged into an executable.
"""

import os
import sys
from pathlib import Path

# Get the current directory
current_dir = Path.cwd()

# Define data files to include
datas = [
    # Voice streaming component
    ('voice_streaming_component.html', '.'),
    
    # Configuration files
    ('config', 'config'),
    
    # Any other required files
    ('utils.py', '.'),
    ('websocket_voice_server.py', '.'),
]

# Add agent and src directories if they exist
if (current_dir / 'agent').exists():
    datas.append(('agent', 'agent'))
    
if (current_dir / 'src').exists():
    datas.append(('src', 'src'))

# Hidden imports - modules that PyInstaller might miss
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'PyQt6.QtWebEngineWidgets',
    'PyQt6.QtWebEngineCore',
    'websockets',
    'langchain',
    'langchain_community',
    'langchain_openai',
    'langchain_litellm',
    'faiss',
    'tiktoken',
    'pypdf',
    'streamlit',
    'fastapi',
    'uvicorn',
    'pydantic',
    'certifi',
    'dotenv',
]

# Binaries to exclude (reduce size)
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'scipy',
    'pandas',
    'jupyter',
    'notebook',
    'IPython',
]

# Analysis configuration
a = Analysis(
    ['voice_widget.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# Remove duplicate files
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# Create executable
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='VoiceStreamingWidget',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # Add icon file path here if you have one
    version_file=None,  # Add version info file here if you have one
)

# Optional: Create a directory distribution instead of single file
# Uncomment the following lines if you prefer a directory distribution:

# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     name='VoiceStreamingWidget'
# )
